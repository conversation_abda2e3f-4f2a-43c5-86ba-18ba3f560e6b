import 'package:get/get.dart';
import 'package:gt_plus/enums/questionnaire_type_enum.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'base_questionnaire_controller.dart';

class COSIQuestionnaireController extends BaseQuestionnaireController {
  @override
  QuestionnaireType get questionnaireType => QuestionnaireType.cosiQuest;

  @override
  String get questionnaireKey => 'setFour'; // COSI is in setFour based on the demo data

  // Observable lists for the 4 dropdown selections and their descriptions
  final RxList<String?> selectedCategories = <String?>[null, null, null, null].obs;
  final RxList<String> descriptions = <String>['', '', '', ''].obs;

  // Update a dropdown selection
  void updateDropdownSelection(int index, String? categoryValue) {
    if (index >= 0 && index < 4) {
      selectedCategories[index] = categoryValue;

      // Update the answer in the base controller
      updateAnswer('dropdown_$index', categoryValue);

      // If selection is cleared, also clear the description
      if (categoryValue == null) {
        descriptions[index] = '';
        updateAnswer('description_$index', '');
      }
    }
  }

  // Update a description text field
  void updateDescription(int index, String description) {
    if (index >= 0 && index < 4) {
      descriptions[index] = description;
      updateAnswer('description_$index', description);
    }
  }

  // Get available categories for a dropdown (excluding already selected ones)
  List<String> getAvailableCategories(int currentIndex) {
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.categories == null) return [];

    final allCategories = questionnaireSet!.categories!.map((c) => c.value).toList();
    final selectedValues = selectedCategories
        .asMap()
        .entries
        .where((entry) => entry.key != currentIndex && entry.value != null)
        .map((entry) => entry.value!)
        .toList();

    return allCategories.where((category) => !selectedValues.contains(category)).toList();
  }

  // Get category label by value
  String getCategoryLabel(String value) {
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.categories == null) return value;

    try {
      final category = questionnaireSet!.categories!.firstWhere((c) => c.value == value);
      return category.label;
    } catch (e) {
      return value;
    }
  }

  @override
  bool validateAnswers() {
    // Check if at least one dropdown is selected
    bool hasSelection = selectedCategories.any((category) => category != null);
    if (!hasSelection) {
      reusableSnackBar(message: 'Please select at least one listening situation');
      return false;
    }

    // Check if all selected dropdowns have corresponding descriptions
    for (int i = 0; i < 4; i++) {
      if (selectedCategories[i] != null && descriptions[i].trim().isEmpty) {
        reusableSnackBar(message: 'Please provide a description for all selected listening situations');
        return false;
      }
    }

    return true;
  }

  @override
  Future<void> loadSavedAnswers() async {
    await super.loadSavedAnswers();

    // Load saved dropdown selections and descriptions
    for (int i = 0; i < 4; i++) {
      final savedCategory = getAnswer('dropdown_$i');
      final savedDescription = getAnswer('description_$i');

      if (savedCategory != null) {
        selectedCategories[i] = savedCategory.toString();
      }

      if (savedDescription != null) {
        descriptions[i] = savedDescription.toString();
      }
    }
  }
}
