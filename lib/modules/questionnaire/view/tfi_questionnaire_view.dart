import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/tfi_questionnaire_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_footer.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

class TFIQuestionnaireView extends GetView<TFIQuestionnaireController> {
  const TFIQuestionnaireView({super.key});

  static const String routeName = "/TFIQuestionnaireView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return _buildContent(context);
    });
  }

  Widget _buildContent(BuildContext context) {
    final questionnaireSet = controller.getQuestionnaireSet();
    if (questionnaireSet == null) {
      return const Center(
        child: Text('Failed to load questionnaire data'),
      );
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            _buildHeader(context, questionnaireSet),
            const SizedBox(height: 20),
            _buildDescription(context, questionnaireSet),
            const SizedBox(height: 24),
            _buildMetaQuestion(context, questionnaireSet),
            const SizedBox(height: 24),
            _buildQuestions(context, questionnaireSet),
            const SizedBox(height: 32),
            _buildSubmitButton(context),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          questionnaireSet.label,
          style: const TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildDescription(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.charcoalBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        questionnaireSet.description,
        style: const TextStyle(
          color: AppColors.charcoalBlue,
          fontSize: 14,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildMetaQuestion(BuildContext context, QuestionnaireSet questionnaireSet) {
    if (questionnaireSet.metaQuest == null || questionnaireSet.metaAnswer == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white.withValues(alpha: 0.1),
        border: Border.all(color: Colors.grey.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '*',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  questionnaireSet.metaQuest!,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Obx(() {
            final selectedValue = controller.metaQuestionAnswer.value;

            return Wrap(
              spacing: 16.0,
              children: questionnaireSet.metaAnswer!.map((metaAnswer) {
                final isSelected = selectedValue == metaAnswer.value;
                return GestureDetector(
                  onTap: () {
                    controller.updateMetaAnswer(metaAnswer.value);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue.shade100 : Colors.white,
                      border: Border.all(
                        color: isSelected ? Colors.blue : Colors.grey.shade300,
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                          size: 20,
                          color: isSelected ? Colors.blue : Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          metaAnswer.label,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: isSelected ? Colors.blue.shade800 : Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            );
          }),
          Obx(() {
            final selectedValue = controller.metaQuestionAnswer.value;
            if (selectedValue == null) {
              return Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Please select an option',
                  style: TextStyle(
                    color: Colors.red.shade600,
                    fontSize: 12,
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  Widget _buildQuestions(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Obx(() {
      // Only show detailed questions if user has tinnitus
      if (!controller.showDetailedQuestions.value) {
        return const SizedBox.shrink();
      }

      if (questionnaireSet.data == null || questionnaireSet.data!.isEmpty) {
        return const SizedBox.shrink();
      }

      int questionCounter = 1;
      return Column(
        children: questionnaireSet.data!.map((section) {
          final sectionWidget = _buildSection(context, section, questionnaireSet, questionCounter);
          // Update counter based on number of questions in this section
          if (section.questions != null) {
            questionCounter += section.questions!.length;
          }
          return sectionWidget;
        }).toList(),
      );
    });
  }

  Widget _buildSection(BuildContext context, QuestionnaireSection section, QuestionnaireSet questionnaireSet, int startingQuestionNumber) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            section.label,
            style: const TextStyle(
              color: AppColors.charcoalBlue,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (section.questions != null)
            ...section.questions!.asMap().entries.map((entry) {
              final index = entry.key;
              final question = entry.value;
              return _buildQuestion(context, question, questionnaireSet, startingQuestionNumber + index);
            }),
        ],
      ),
    );
  }

  Widget _buildQuestion(BuildContext context, QuestionnaireQuestion question, QuestionnaireSet questionnaireSet, int questionNumber) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$questionNumber. ${question.title}',
            style: const TextStyle(
              color: AppColors.charcoalBlue,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildAnswerWidget(context, question, questionnaireSet),
        ],
      ),
    );
  }

  Widget _buildAnswerWidget(BuildContext context, QuestionnaireQuestion question, QuestionnaireSet questionnaireSet) {
    // Check if question has scale properties for slider
    if (question.minScale != null && question.maxScale != null) {
      return _buildSliderAnswer(context, question);
    }

    // Check if questionnaire set has options (like APHAB)
    if (questionnaireSet.options != null && questionnaireSet.options!.isNotEmpty) {
      return _buildRadioAnswer(context, question, questionnaireSet.options!);
    }

    // Default to text input
    return _buildTextAnswer(context, question);
  }

  Widget _buildSliderAnswer(BuildContext context, QuestionnaireQuestion question) {
    return Obx(() {
      final currentValue = controller.getAnswer(question.value) as double? ?? question.minScale!.toDouble();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(question.startLabel ?? '${question.minScale}', style: const TextStyle(fontSize: 12)),
              Text('${currentValue.toInt()}', style: const TextStyle(fontWeight: FontWeight.bold)),
              Text(question.endLabel ?? '${question.maxScale}', style: const TextStyle(fontSize: 12)),
            ],
          ),
          Slider(
            value: currentValue,
            min: question.minScale!.toDouble(),
            max: question.maxScale!.toDouble(),
            divisions: question.maxScale! - question.minScale!,
            onChanged: (value) {
              controller.updateAnswer(question.value, value);
            },
          ),
        ],
      );
    });
  }

  Widget _buildRadioAnswer(BuildContext context, QuestionnaireQuestion question, List<QuestionnaireOption> options) {
    return Obx(() {
      final selectedValue = controller.getAnswer(question.value);

      return Wrap(
        spacing: 8.0,
        runSpacing: 4.0,
        children: options.map((option) {
          final isSelected = selectedValue == option.value;
          return GestureDetector(
            onTap: () {
              controller.updateAnswer(question.value, option.value);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue.shade100 : Colors.grey.shade100,
                border: Border.all(
                  color: isSelected ? Colors.blue : Colors.grey.shade300,
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                    size: 16,
                    color: isSelected ? Colors.blue : Colors.grey,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    option.label,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: isSelected ? Colors.blue.shade800 : Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      );
    });
  }

  Widget _buildTextAnswer(BuildContext context, QuestionnaireQuestion question) {
    return TextFormField(
      initialValue: controller.getAnswer(question.value)?.toString() ?? '',
      style: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      decoration: InputDecoration(
        hintText: 'Enter your answer',
        hintStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        isDense: true,
      ),
      onChanged: (value) {
        controller.updateAnswer(question.value, value);
      },
    );
  }

  Widget _buildSubmitButton(BuildContext context) {
    return Obx(() => ReusableButton(
          width: 150,
          title: 'Submit',
          isLoading: controller.isSubmitting.value,
          onTap: controller.submitQuestionnaire,
        ));
  }
}
