import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/aphab_questionnaire_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_footer.dart';
import 'package:gt_plus/utils/reusableWidgets/questionnaire_widgets.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

class APHABQuestionnaireView extends GetView<APHABQuestionnaireController> {
  const APHABQuestionnaireView({super.key});

  static const String routeName = "/APHABQuestionnaireView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return _buildContent(context);
    });
  }

  Widget _buildContent(BuildContext context) {
    final questionnaireSet = controller.getQuestionnaireSet();
    if (questionnaireSet == null) {
      return const Center(
        child: Text('Failed to load questionnaire data'),
      );
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            QuestionnaireHeader(title: questionnaireSet.label),
            const SizedBox(height: 20),
            QuestionnaireReadOnlyIndicator(controller: controller),
            QuestionnaireDescription(description: questionnaireSet.description),
            const SizedBox(height: 24),
            _buildQuestions(context, questionnaireSet),
            const SizedBox(height: 32),
            QuestionnaireActionButtons(controller: controller),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }



  Widget _buildQuestions(
      BuildContext context, QuestionnaireSet questionnaireSet) {
    if (questionnaireSet.data == null || questionnaireSet.data!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: questionnaireSet.data!.asMap().entries.map((entry) {
        final index = entry.key;
        final section = entry.value;
        // APHAB has a flat structure where each "section" is actually a question
        return QuestionnaireQuestionContainer(
          questionNumber: index + 1,
          questionText: section.title ?? section.label,
          answerWidget: _buildAPHABAnswerWidget(context, section, questionnaireSet),
        );
      }).toList(),
    );
  }

  Widget _buildAPHABAnswerWidget(BuildContext context,
      QuestionnaireSection section, QuestionnaireSet questionnaireSet) {
    // APHAB uses options at the questionnaire level
    if (questionnaireSet.options != null &&
        questionnaireSet.options!.isNotEmpty) {
      return QuestionnaireRadioGroup(
        controller: controller,
        questionId: section.value,
        options: questionnaireSet.options!,
      );
    }

    return const SizedBox.shrink();
  }
}
