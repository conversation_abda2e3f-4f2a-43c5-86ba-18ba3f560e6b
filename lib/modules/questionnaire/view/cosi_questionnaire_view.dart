import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/cosi_questionnaire_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_footer.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

class COSIQuestionnaireView extends GetView<COSIQuestionnaireController> {
  const COSIQuestionnaireView({super.key});

  static const String routeName = "/COSIQuestionnaireView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return _buildContent(context);
    });
  }

  Widget _buildContent(BuildContext context) {
    final questionnaireSet = controller.getQuestionnaireSet();
    if (questionnaireSet == null) {
      return const Center(
        child: Text('Failed to load questionnaire data'),
      );
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            _buildHeader(context, questionnaireSet),
            const SizedBox(height: 20),
            _buildDescription(context, questionnaireSet),
            const SizedBox(height: 24),
            _buildCategories(context, questionnaireSet),
            const SizedBox(height: 32),
            _buildSubmitButton(context),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          questionnaireSet.label,
          style: const TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildDescription(
      BuildContext context, QuestionnaireSet questionnaireSet) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.charcoalBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        questionnaireSet.description,
        style: const TextStyle(
          color: AppColors.charcoalBlue,
          fontSize: 14,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildCategories(
      BuildContext context, QuestionnaireSet questionnaireSet) {
    if (questionnaireSet.categories == null ||
        questionnaireSet.categories!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select up to 4 listening situations and provide descriptions:',
          style: TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(
            4, (index) => _buildDropdownItem(context, questionnaireSet, index)),
      ],
    );
  }

  Widget _buildDropdownItem(
      BuildContext context, QuestionnaireSet questionnaireSet, int index) {
    return Obx(() {
      final availableCategories = controller.getAvailableCategories(index);
      final selectedCategory = controller.selectedCategories[index];
      final description = controller.descriptions[index];

      return Container(
        margin: const EdgeInsets.only(bottom: 24),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border:
              Border.all(color: AppColors.charcoalBlue.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Listening Situation ${index + 1}',
              style: const TextStyle(
                color: AppColors.charcoalBlue,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: selectedCategory,
              decoration: InputDecoration(
                hintText: 'Select a listening situation',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('Select a situation'),
                ),
                ...availableCategories.map((categoryValue) {
                  final category = questionnaireSet.categories!
                      .firstWhere((c) => c.value == categoryValue);
                  return DropdownMenuItem<String>(
                    value: categoryValue,
                    child: Text(
                      category.label,
                      style: const TextStyle(fontSize: 14),
                    ),
                  );
                }),
              ],
              onChanged: (value) {
                controller.updateDropdownSelection(index, value);
              },
            ),
            if (selectedCategory != null) ...[
              const SizedBox(height: 16),
              Text(
                'Description for ${controller.getCategoryLabel(selectedCategory)}:',
                style: const TextStyle(
                  color: AppColors.charcoalBlue,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                initialValue: description,
                decoration: InputDecoration(
                  hintText:
                      'Please describe your experience with this listening situation',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.all(12),
                ),
                maxLines: 3,
                onChanged: (value) {
                  controller.updateDescription(index, value);
                },
              ),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildSubmitButton(BuildContext context) {
    return Obx(() => ReusableButton(
          width: 150,
          title: 'Submit',
          isLoading: controller.isSubmitting.value,
          onTap: controller.submitQuestionnaire,
        ));
  }
}
