import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../appConst/app_colors.dart';
import '../../models/questionnaire_models.dart';
import '../../modules/questionnaire/controller/base_questionnaire_controller.dart';

/// Reusable dropdown field for questionnaires
class QuestionnaireDropdownField<T> extends StatelessWidget {
  final BaseQuestionnaireController controller;
  final String questionId;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final String hintText;
  final String? labelText;
  final ValueChanged<T?>? onChanged;
  final String? Function(T?)? validator;
  final EdgeInsets? contentPadding;

  const QuestionnaireDropdownField({
    super.key,
    required this.controller,
    required this.questionId,
    required this.value,
    required this.items,
    required this.hintText,
    this.labelText,
    this.onChanged,
    this.validator,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isReadOnly = controller.isReadOnly.value;
      
      return DropdownButtonFormField<T>(
        value: value,
        decoration: InputDecoration(
          hintText: hintText,
          labelText: labelText,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: contentPadding ?? 
              const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          filled: isReadOnly,
          fillColor: isReadOnly ? Colors.grey.shade200 : null,
        ),
        items: items,
        onChanged: isReadOnly ? null : (onChanged ?? (value) {
          controller.updateAnswer(questionId, value);
        }),
        validator: validator,
      );
    });
  }
}

/// Reusable text field for questionnaires
class QuestionnaireTextField extends StatelessWidget {
  final BaseQuestionnaireController controller;
  final String questionId;
  final String? initialValue;
  final String hintText;
  final String? labelText;
  final int? maxLines;
  final int? minLines;
  final TextInputType? keyboardType;
  final ValueChanged<String>? onChanged;
  final String? Function(String?)? validator;
  final EdgeInsets? contentPadding;
  final TextEditingController? textController;

  const QuestionnaireTextField({
    super.key,
    required this.controller,
    required this.questionId,
    this.initialValue,
    required this.hintText,
    this.labelText,
    this.maxLines = 1,
    this.minLines,
    this.keyboardType,
    this.onChanged,
    this.validator,
    this.contentPadding,
    this.textController,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isReadOnly = controller.isReadOnly.value;
      
      return TextFormField(
        controller: textController,
        initialValue: textController == null ? initialValue : null,
        readOnly: isReadOnly,
        decoration: InputDecoration(
          hintText: hintText,
          labelText: labelText,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: contentPadding ?? const EdgeInsets.all(12),
          filled: isReadOnly,
          fillColor: isReadOnly ? Colors.grey.shade200 : null,
        ),
        maxLines: maxLines,
        minLines: minLines,
        keyboardType: keyboardType,
        onChanged: isReadOnly ? null : (onChanged ?? (value) {
          controller.updateAnswer(questionId, value);
        }),
        validator: validator,
      );
    });
  }
}

/// Reusable meta question widget (like TFI's yes/no question)
class QuestionnaireMetaQuestion extends StatelessWidget {
  final BaseQuestionnaireController controller;
  final String questionText;
  final List<QuestionnaireMetaAnswer> options;
  final dynamic selectedValue;
  final ValueChanged<dynamic> onChanged;
  final EdgeInsets? padding;

  const QuestionnaireMetaQuestion({
    super.key,
    required this.controller,
    required this.questionText,
    required this.options,
    required this.selectedValue,
    required this.onChanged,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        border: Border.all(color: Colors.blue.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.help_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  questionText,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Obx(() {
            final isReadOnly = controller.isReadOnly.value;

            return Wrap(
              spacing: 16.0,
              children: options.map((option) {
                final isSelected = selectedValue == option.value;
                return GestureDetector(
                  onTap: isReadOnly ? null : () => onChanged(option.value),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                    decoration: BoxDecoration(
                      color: isReadOnly 
                          ? Colors.grey.shade200 
                          : (isSelected ? Colors.blue.shade100 : Colors.white),
                      border: Border.all(
                        color: isReadOnly 
                            ? Colors.grey.shade400
                            : (isSelected ? Colors.blue : Colors.grey.shade300),
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                          size: 20,
                          color: isReadOnly 
                              ? Colors.grey.shade500
                              : (isSelected ? Colors.blue : Colors.grey),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          option.label,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: isReadOnly 
                                ? Colors.grey.shade600
                                : (isSelected ? Colors.blue.shade800 : Colors.black87),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            );
          }),
        ],
      ),
    );
  }
}

/// Reusable section widget for organizing questions
class QuestionnaireSection extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? backgroundColor;

  const QuestionnaireSection({
    super.key,
    required this.title,
    required this.children,
    this.padding,
    this.margin,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 24),
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: AppColors.charcoalBlue,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }
}
