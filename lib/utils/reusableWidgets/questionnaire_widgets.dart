import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../appConst/app_colors.dart';
import 'reusable_button.dart';
import '../../models/questionnaire_models.dart';
import '../../modules/questionnaire/controller/base_questionnaire_controller.dart';

/// Reusable read-only indicator for questionnaires
class QuestionnaireReadOnlyIndicator extends StatelessWidget {
  final BaseQuestionnaireController controller;
  final String? customMessage;

  const QuestionnaireReadOnlyIndicator({
    super.key,
    required this.controller,
    this.customMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Read-Only Mode Indicator
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border.all(color: Colors.blue.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lock_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  customMessage ?? 
                  "Read-Only Mode: This questionnaire has been completed. Use 'Edit' to make changes or 'Reset' to start over.",
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else if (controller.isFormCompleted.value && !controller.isReadOnly.value) {
        // Edit Mode Indicator (for previously completed forms)
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            border: Border.all(color: Colors.orange.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Edit Mode: You are now editing a previously completed questionnaire. Changes will be saved when you submit.",
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return const SizedBox.shrink();
      }
    });
  }
}

/// Reusable action buttons for questionnaires
class QuestionnaireActionButtons extends StatelessWidget {
  final BaseQuestionnaireController controller;
  final VoidCallback? onSubmit;
  final VoidCallback? onReset;
  final VoidCallback? onEdit;
  final String submitButtonText;
  final String resetButtonText;
  final String editButtonText;

  const QuestionnaireActionButtons({
    super.key,
    required this.controller,
    this.onSubmit,
    this.onReset,
    this.onEdit,
    this.submitButtonText = 'Submit',
    this.resetButtonText = 'Reset',
    this.editButtonText = 'Edit',
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: context.width * .22,
              title: resetButtonText,
              color: Colors.red,
              borderColor: Colors.red,
              onTap: onReset ?? () => controller.resetForm(),
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: context.width * .22,
              title: editButtonText,
              onTap: onEdit ?? () => controller.enableEditMode(),
            ),
          ],
        );
      } else {
        // Show Submit button when in edit mode
        return ReusableButton(
          width: context.width * .22,
          title: submitButtonText,
          onTap: onSubmit ?? controller.submitQuestionnaire,
          isLoading: controller.isSubmitting.value,
        );
      }
    });
  }
}

/// Reusable header for questionnaires
class QuestionnaireHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;

  const QuestionnaireHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.titleStyle,
    this.subtitleStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: titleStyle ?? const TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (subtitle != null) ...[
          const SizedBox(height: 8),
          Text(
            subtitle!,
            style: subtitleStyle ?? const TextStyle(
              color: AppColors.charcoalBlue,
              fontSize: 16,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ],
    );
  }
}

/// Reusable description container for questionnaires
class QuestionnaireDescription extends StatelessWidget {
  final String description;
  final Color? backgroundColor;
  final TextStyle? textStyle;
  final EdgeInsets? padding;

  const QuestionnaireDescription({
    super.key,
    required this.description,
    this.backgroundColor,
    this.textStyle,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.charcoalBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        description,
        style: textStyle ?? const TextStyle(
          color: AppColors.charcoalBlue,
          fontSize: 14,
          height: 1.5,
        ),
      ),
    );
  }
}

/// Reusable radio group for questionnaires
class QuestionnaireRadioGroup extends StatelessWidget {
  final BaseQuestionnaireController controller;
  final String questionId;
  final List<QuestionnaireOption> options;
  final double spacing;
  final double runSpacing;
  final bool isCompact;

  const QuestionnaireRadioGroup({
    super.key,
    required this.controller,
    required this.questionId,
    required this.options,
    this.spacing = 8.0,
    this.runSpacing = 4.0,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final selectedValue = controller.getAnswer(questionId);
      final isReadOnly = controller.isReadOnly.value;

      return Wrap(
        spacing: spacing,
        runSpacing: runSpacing,
        children: options.map((option) {
          final isSelected = selectedValue == option.value;
          return GestureDetector(
            onTap: isReadOnly ? null : () {
              controller.updateAnswer(questionId, option.value);
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isCompact ? 8 : 12,
                vertical: isCompact ? 6 : 8,
              ),
              decoration: BoxDecoration(
                color: isReadOnly 
                    ? Colors.grey.shade200 
                    : (isSelected ? Colors.blue.shade100 : Colors.grey.shade100),
                border: Border.all(
                  color: isReadOnly 
                      ? Colors.grey.shade400
                      : (isSelected ? Colors.blue : Colors.grey.shade300),
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(isCompact ? 16 : 20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isSelected
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked,
                    size: isCompact ? 14 : 16,
                    color: isReadOnly 
                        ? Colors.grey.shade500
                        : (isSelected ? Colors.blue : Colors.grey),
                  ),
                  SizedBox(width: isCompact ? 4 : 6),
                  Text(
                    option.label,
                    style: TextStyle(
                      fontSize: isCompact ? 12 : 14,
                      fontWeight: FontWeight.w400,
                      color: isReadOnly 
                          ? Colors.grey.shade600
                          : (isSelected ? Colors.blue.shade800 : Colors.black87),
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      );
    });
  }
}

/// Reusable question container for questionnaires
class QuestionnaireQuestionContainer extends StatelessWidget {
  final String questionText;
  final Widget answerWidget;
  final int? questionNumber;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const QuestionnaireQuestionContainer({
    super.key,
    required this.questionText,
    required this.answerWidget,
    this.questionNumber,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 24),
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            questionNumber != null
                ? '$questionNumber. $questionText'
                : questionText,
            style: const TextStyle(
              color: AppColors.charcoalBlue,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          answerWidget,
        ],
      ),
    );
  }
}

/// Reusable slider widget for questionnaires
class QuestionnaireSlider extends StatelessWidget {
  final BaseQuestionnaireController controller;
  final String questionId;
  final double min;
  final double max;
  final int? divisions;
  final String? startLabel;
  final String? endLabel;
  final bool showValue;

  const QuestionnaireSlider({
    super.key,
    required this.controller,
    required this.questionId,
    required this.min,
    required this.max,
    this.divisions,
    this.startLabel,
    this.endLabel,
    this.showValue = true,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final currentValue = (controller.getAnswer(questionId) as double?) ?? min;
      final isReadOnly = controller.isReadOnly.value;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showValue || startLabel != null || endLabel != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  startLabel ?? min.toInt().toString(),
                  style: const TextStyle(fontSize: 12),
                ),
                if (showValue)
                  Text(
                    currentValue.toInt().toString(),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                Text(
                  endLabel ?? max.toInt().toString(),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          Slider(
            value: currentValue,
            min: min,
            max: max,
            divisions: divisions ?? (max - min).toInt(),
            onChanged: isReadOnly ? null : (value) {
              controller.updateAnswer(questionId, value);
            },
          ),
        ],
      );
    });
  }
}
